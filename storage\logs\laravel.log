[2025-05-27 16:36:21] local.INFO: User kasir logged in successfully. employee_id: KASIR001, site_id: DH  
[2025-05-28 09:22:39] local.INFO: User kasir logged in successfully. employee_id: KASIR001, site_id: DH  
[2025-05-28 11:28:12] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-05-28 11:28:15] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 11:28:15] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 11:28:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 11:28:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:11:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:13:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:34:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:49:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:50:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:50:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:50:54] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-05-28 12:51:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:51:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:51:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:51:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:52:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:52:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:52:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:52:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:52:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:52:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:39] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 12:53:39] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:53:39] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:53:39] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:53:39] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:53:39] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 12:53:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:53:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:54:00] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 12:54:00] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:54:00] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:54:00] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:54:00] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 12:54:00] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 12:54:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:10:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:10:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:14:38] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 13:14:38] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:14:38] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:14:38] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:14:38] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:14:38] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 13:14:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:14:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:17:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:17:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:47:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:47:47] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:47:55] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:00] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:07] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:12] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:15] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:24] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:29] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:31] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":"TYRE","site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:31] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:34] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":"AC","site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:35] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:37] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:42] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:48:53] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 13:49:29] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 13:49:30] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:49:30] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:49:30] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:49:30] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:49:30] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 13:49:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:49:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:50:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:50:58] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 13:50:58] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:50:58] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:50:58] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:50:58] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 13:50:58] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 13:50:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 13:51:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 14:26:27] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 14:26:29] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 14:26:33] local.INFO: getDivisionPartsDetail called with parameters: {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":"AC","site":null} 
[2025-05-28 14:26:33] local.INFO: Processed parameters: {"start_date":"2025-05-01 00:00:00","end_date":"2025-05-28 23:59:59","division_filter":"AC","site_filter":null} 
[2025-05-28 14:26:33] local.INFO: Transaction query results: {"total_records":228,"division_filter":"AC","site_filter":null,"date_range":["2025-05-01 00:00:00","2025-05-28 23:59:59"]} 
[2025-05-28 14:26:41] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 14:26:41] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 14:26:41] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 14:26:41] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 14:26:41] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 14:26:41] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 14:26:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 15:05:21] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 15:05:25] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-28","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-28 23:59:59"} 
[2025-05-28 15:05:32] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 15:05:32] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 15:05:32] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 15:05:32] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 15:05:32] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 15:05:32] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 15:05:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 15:05:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 16:13:59] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 16:13:59] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:13:59] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:13:59] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:13:59] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:13:59] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 16:13:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 16:14:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 16:15:16] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 16:15:16] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:15:16] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:15:16] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:15:16] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 16:15:16] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 16:15:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 16:15:25] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-05-28 16:15:43] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-28 16:15:50] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-28 16:16:46] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-28 16:19:09] local.INFO: Unit data: {"id":24,"site_id":"PPA","unit_code":"HD7897","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 375","noSPB":"PWB-PPA-BIB\/V\/2025\/375","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-16T11:02:11.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":4,"created_at":null,"updated_at":"2025-05-26T01:40:20.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]}  
[2025-05-28 16:19:09] local.INFO: Parts data: [{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":4,"created_at":null,"updated_at":"2025-05-26T01:40:20.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]  
[2025-05-28 16:19:15] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/376  
[2025-05-28 16:19:20] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/376  
[2025-05-28 16:19:25] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/376  
[2025-05-28 16:28:34] local.INFO: Unit data: {"id":24,"site_id":"PPA","unit_code":"HD7897","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 375","noSPB":"PWB-PPA-BIB\/V\/2025\/375","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-16T11:02:11.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":4,"created_at":null,"updated_at":"2025-05-26T01:40:20.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]}  
[2025-05-28 16:28:34] local.INFO: Parts data: [{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":4,"created_at":null,"updated_at":"2025-05-26T01:40:20.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]  
[2025-05-28 16:28:40] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/376  
[2025-05-28 16:31:11] local.INFO: Unit data: {"id":24,"site_id":"PPA","unit_code":"HD7897","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 375","noSPB":"PWB-PPA-BIB\/V\/2025\/375","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-16T11:02:11.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":4,"created_at":null,"updated_at":"2025-05-26T01:40:20.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]}  
[2025-05-28 16:31:11] local.INFO: Parts data: [{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":4,"created_at":null,"updated_at":"2025-05-26T01:40:20.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]  
[2025-05-28 16:31:24] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/376  
[2025-05-28 16:31:33] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/376  
[2025-05-28 16:31:37] local.INFO: Unit data: {"id":23,"site_id":"PPA","unit_code":"H57105AMM","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 373","noSPB":"PWB-PPA-BIB\/V\/2025\/373","created_at":"2025-05-08T16:24:50.000000Z","updated_at":"2025-05-08T16:26:07.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":43,"unit_id":23,"part_inventory_id":2020,"quantity":1,"price":2820000,"eum":"AE","created_at":"2025-05-08T16:24:50.000000Z","updated_at":"2025-05-08T16:24:50.000000Z","part_inventory":{"part_inventory_id":2020,"part_code":"MB-564587-PWB","site_part_name":"MOTOR BLOWER HD 465 \/ 785 - 7R","site_id":"PPA","priority":false,"min_stock":10,"max_stock":20,"stock_quantity":40,"created_at":null,"updated_at":"2025-05-24T11:25:53.000000Z","price":"2820000.00","item_code":null,"part":{"part_code":"MB-564587-PWB","part_name":"MOTOR BLOWER HD 465 \/ 785 - 7R","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]}  
[2025-05-28 16:31:37] local.INFO: Parts data: [{"id":43,"unit_id":23,"part_inventory_id":2020,"quantity":1,"price":2820000,"eum":"AE","created_at":"2025-05-08T16:24:50.000000Z","updated_at":"2025-05-08T16:24:50.000000Z","part_inventory":{"part_inventory_id":2020,"part_code":"MB-564587-PWB","site_part_name":"MOTOR BLOWER HD 465 \/ 785 - 7R","site_id":"PPA","priority":false,"min_stock":10,"max_stock":20,"stock_quantity":40,"created_at":null,"updated_at":"2025-05-24T11:25:53.000000Z","price":"2820000.00","item_code":null,"part":{"part_code":"MB-564587-PWB","part_name":"MOTOR BLOWER HD 465 \/ 785 - 7R","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]  
[2025-05-28 16:31:41] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/374  
[2025-05-28 16:31:58] local.INFO: Unit data: {"id":24,"site_id":"PPA","unit_code":"HD7897","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 376","noSPB":"PWB-PPA-BIB\/V\/2025\/376","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-28T08:31:33.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":3,"created_at":null,"updated_at":"2025-05-28T08:31:33.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]}  
[2025-05-28 16:31:58] local.INFO: Parts data: [{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":3,"created_at":null,"updated_at":"2025-05-28T08:31:33.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]  
[2025-05-28 16:32:02] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/377  
[2025-05-28 16:35:19] local.INFO: Unit data: {"id":24,"site_id":"PPA","unit_code":"HD7897","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 377","noSPB":"PWB-PPA-BIB\/V\/2025\/377","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-28T08:32:02.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":2,"created_at":null,"updated_at":"2025-05-28T08:32:02.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]}  
[2025-05-28 16:35:19] local.INFO: Parts data: [{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":false,"min_stock":0,"max_stock":0,"stock_quantity":2,"created_at":null,"updated_at":"2025-05-28T08:32:02.000000Z","price":"666333.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":0,"purchase_price":null,"eum":"EA","created_at":null,"updated_at":null}}}]  
[2025-05-28 16:35:22] local.INFO: SPB Number: PWB-PPA-BIB/V/2025/378  
[2025-05-28 16:35:45] local.INFO: Part update details: {"part_name":"FREON KLEA","part_id":"2011","part_code":"FRN-KLEA-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":1.0} 
[2025-05-28 16:43:04] local.INFO: Part update details: {"part_name":"FREON KLEA","part_id":"2011","part_code":"FRN-KLEA-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":1.0} 
[2025-05-31 07:51:11] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-05-31 07:51:23] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 07:51:24] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 07:51:24] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 07:51:24] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 07:51:24] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 07:51:24] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 07:51:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:51:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:51:32] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:51:37] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:51:42] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:54:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:55:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:56:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:56:37] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 07:57:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:34:40] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 08:34:41] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:34:41] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:34:41] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:34:41] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:34:41] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 08:34:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:34:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:35:03] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 08:35:03] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:03] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:03] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:03] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:03] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 08:35:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:35:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:35:22] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 08:35:22] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:22] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:22] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:22] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 08:35:22] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 08:35:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:35:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 08:44:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 09:02:36] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 09:02:36] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:36] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:36] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:36] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:36] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 09:02:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 09:02:48] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 09:02:48] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:48] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:48] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:48] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:02:48] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 09:02:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 09:08:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 09:08:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-31 09:08:30] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-31 09:08:31] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:08:31] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:08:31] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:08:31] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-31 09:08:31] local.INFO: Sites Data Response {"count":4} 
[2025-05-31 09:08:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
